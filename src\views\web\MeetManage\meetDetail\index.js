import React, { useEffect, useState, useMemo } from 'react'
import { Spin, message, TimePicker, InputNumber } from 'antd'
import { Input, Select, Radio, FormItemGrid, DatePicker, FormCard, FormBlock, FormMegaLayout } from '@formily/antd-components'
import { SchemaMarkupForm, createAsyncFormActions } from '@formily/antd'
import { useParams, useHistory } from 'react-router-dom'
import SelectDept from 'ROOT/components/Formily/deptSelect'
import SelectMember from 'ROOT/components/Formily/userSelect'
import EditableInput from 'ROOT/components/Formily/Editable'
import { css } from 'emotion'
// import PageHeader from '../components/pageHeader'
import schema from './schema'
import AgendaArrayField from './components/AgendaSection'
import FormActions from './components/FormActions'
import service from 'ROOT/service'
import './index.css'
const MeetManage = () => {
  const { id } = useParams()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [editable, setEditable] = useState(true)
  const [initValue, setInitValue] = useState({})
  const [isEdit, setIsEdit] = useState(false)
  const actions = useMemo(() => createAsyncFormActions(), [])

  const uesEffects = () => {
    // 可以在这里添加表单联动效果
  }

  // 数据回填
  useEffect(() => {
    const fetchData = async () => {
      if (id && id !== 'new') {
        try {
          setLoading(true)
          setIsEdit(true)
          const response = await service.getMeetingDetail({ id })

          if (response) {
            setInitValue(response)
            // 设置表单初始值
            actions.setFormState(state => {
              state.values = response
            })
          }
        } catch (error) {
          console.error('获取会议详情失败:', error)
          message.error('获取会议详情失败')
        } finally {
          setLoading(false)
        }
      } else {
        // 新建模式
        setIsEdit(false)
        const defaultValues = {
          topicsList: [] // 默认空的议题列表
        }
        setInitValue(defaultValues)
        actions.setFormState(state => {
          state.values = defaultValues
        })
      }
    }
    fetchData()
  }, [id, actions])
  const expressionScope = {

  }

  // 标记自定义组件为字段组件
  AgendaArrayField.isFieldComponent = true

  const components = {
    Input,
    Select,
    DatePicker,
    FormCard,
    SelectDept,
    SelectMember,
    EditableInput,
    AgendaArrayField,
    TimePicker,
    InputNumber,
    FormBlock,
    FormMegaLayout,
    'mega-layout': FormMegaLayout,
  }

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true)

      // 验证表单
      await actions.validate()

      // 获取表单数据
      const formState = await actions.getFormState()
      const values = formState.values
      console.log('表单数据:', values)

      // 额外验证议题数据
      if (values.agendaList && values.agendaList.length > 0) {
        const invalidAgendas = []
        values.agendaList.forEach((agenda, index) => {
          const errors = []
          if (!agenda.reporter) errors.push('汇报人')
          if (!agenda.startTime) errors.push('开始时间')
          if (!agenda.duration) errors.push('汇报时长')
          if (!agenda.agendaName) errors.push('议题名称')
          if (!agenda.reportUnit) errors.push('汇报单位')

          if (errors.length > 0) {
            invalidAgendas.push(`议题${index + 1}: ${errors.join('、')}不能为空`)
          }
        })

        if (invalidAgendas.length > 0) {
          message.error(`请完善以下信息：\n${invalidAgendas.join('\n')}`)
          return
        }
      }
      const param = { "name": "广西移动第三季度会议", "meetingDate": 1748923757000, "meetingType": "专题会", "meetingRoom": "103", "startTime": 1748923757000, "leaderList": [{ "uid": 8888967964, "name": "陈涛" }], "operatorList": [{ "uid": 8888981688, "name": "邱宝华" }], "topicsList": [{ "topicsName": "三季度经济分析", "topicsTitle": "议题一", "topicsStartTime": 1748923757000, "topicsDuration": 30, "sequence": 1, "reportDept": [{ "orgId": 10032, "orgName": "区公司", "deptId": 529957, "deptName": "单位直属" }], "reportList": [{ "uid": 8888971262, "name": "黄涛" }], "attendanceDept": [{ "orgId": 10032, "orgName": "区公司", "deptId": 529957, "deptName": "单位直属" }], "attendanceList": [{ "uid": 8888967748, "name": "何玲" }] }, { "topicsName": "三季度经营情况", "topicsTitle": "议题二", "topicsStartTime": 1748925557000, "topicsDuration": 30, "sequence": 1, "reportDept": [{ "orgId": 10032, "orgName": "区公司", "deptId": 529957, "deptName": "单位直属" }], "reportList": [{ "uid": 8888979443, "name": "左会军" }], "attendanceDept": [{ "orgId": 10032, "orgName": "区公司", "deptId": 529957, "deptName": "单位直属" }], "attendanceList": [{ "uid": 8888973582, "name": "王一秋" }] }, { "topicsName": "三季度总结", "topicsTitle": "议题二", "topicsStartTime": 1748925557000, "topicsDuration": 30, "sequence": 1, "reportDept": [{ "orgId": 10032, "orgName": "区公司", "deptId": 529957, "deptName": "单位直属" }], "reportList": [{ "uid": 8888979443, "name": "左会军" }], "attendanceDept": [{ "orgId": 10032, "orgName": "区公司", "deptId": 529957, "deptName": "单位直属" }], "attendanceList": [{ "uid": 8888973582, "name": "王一秋" }] }] }
      // 调用API保存数据
      const saveData = {
        ...values,
        leaderList: [{ uid: 'test1', name: 'test1' }, { uid: 'test2', name: 'test2' }],
        operatorList: [{ uid: 'test3', name: 'test3' }, { uid: 'test4', name: 'test4' }],
        topicsList: values.topicsList.map(agenda => ({
          topicsName: 'test',
          topicsStartTime: 16356545234123,
          topicsTitle: 'test',
          topicsDuration: 30,
          sequence: 1,
          reportDept: [],
          reportList: [],
          attendanceDept: [],
          attendanceList: [],
        })),
        id: isEdit ? id : undefined, // 编辑时传入id，新建时不传
      }

      await service.saveMeeting(param)
      message.success(isEdit ? '更新成功' : '创建成功')

      // 保存成功后返回列表页
      history.push('/web/MeetManage/meetList')
    } catch (error) {
      console.error('提交失败:', error)
      if (error.name === 'ValidateError') {
        console.log('验证错误详情:', error)
        message.error('请检查表单填写是否完整')
      } else {
        message.error(isEdit ? '更新失败' : '创建失败')
      }
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    history.push('/web/MeetManage/meetList')
  }
  return (
    <div className="meet-manage-container">
      <Spin
        spinning={loading}
        style={{ height: '100vh', overflow: 'hidden', maxHeight: 'initial' }}
      >
        {/* <PageHeader
          title={'创建会议'}
          hasBack={true}
        /> */}
        <div
          className={css`
          overflow: auto;
          height: 100%;
        `}
        >
          <div className="meet-manage-form" style={{ paddingBottom: '80px' }}>
            <div className={css`
                font-size: 18px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 24px;
                text-align: center;
              `}>{isEdit ? '编辑会议' : '创建会议'}</div>
            <div className={css`
                font-size: 16px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 16px;
              `}>会议基本信息</div>
            <SchemaMarkupForm
              schema={schema()}
              components={components}
              actions={actions}
              effects={() => {
                uesEffects()
              }}
              initialValues={initValue}
              expressionScope={{ ...expressionScope }}
              previewPlaceholder='-'
              editable={editable}
            >
            </SchemaMarkupForm>
          </div>

          <FormActions
            onCancel={handleCancel}
            onSubmit={handleSubmit}
            loading={loading}
          />
        </div>
      </Spin>
    </div>
  )
}

export default MeetManage
